{"go.useLanguageServer": true, "go.gopath": "", "go.toolsManagement.autoUpdate": true, "go.formatTool": "gofmt", "go.lintTool": "golangci-lint", "go.lintOnSave": "package", "go.vetOnSave": "package", "go.buildOnSave": "package", "go.testOnSave": true, "go.coverOnSave": false, "go.testFlags": ["-v"], "go.toolsEnvVars": {"GO111MODULE": "on", "GOPROXY": "https://goproxy.cn,direct"}, "gopls": {"build.experimentalWorkspaceModule": true, "ui.completion.usePlaceholders": true, "ui.diagnostic.analyses": {"unusedparams": true, "nilness": true, "unusedwrite": true, "shadow": true}}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "[go]": {"editor.insertSpaces": true, "editor.tabSize": 4, "editor.formatOnSave": true}, "files.autoSave": "after<PERSON>elay", "files.exclude": {"**/__debug_bin": true, "**/*.test": true}}