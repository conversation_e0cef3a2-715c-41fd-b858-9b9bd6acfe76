FROM golang:1.21 AS builder

WORKDIR /app
COPY . .
RUN go build -o rtk-service ./cmd/main.go

FROM centos:7
RUN yum groupinstall "Development Tools" -y && \
    yum install -y git gcc make

# Build RTKLIB
RUN git clone https://github.com/rtklibexplorer/RTKLIB.git /opt/RTKLIB && \
    cd /opt/RTKLIB/app/convbin/gcc && make && \
    cd /opt/RTKLIB/app/rtkpost/gcc && make

COPY --from=builder /app/rtk-service /app/rtk-service
ENTRYPOINT ["/app/rtk-service"]
