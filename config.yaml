# RTK Service Configuration

# FTP Server Configuration
ftp:
  host: ftp.example.com
  user: ftpuser
  password: ftppass
  port: 21
  timeout: 30  # connection timeout in seconds

# Server Configuration
server:
  port: 8000
  host: 0.0.0.0
  read_timeout: 30  # in seconds
  write_timeout: 30  # in seconds

# Processing Configuration
processing:
  max_concurrent_tasks: 10
  task_timeout: 3600  # in seconds
  output_directory: "/data/rtk/results"

# Logging Configuration
logging:
  level: info  # debug, info, warn, error
  file: "/var/log/rtk-service.log"
  max_size: 100  # MB
  max_backups: 3
  max_age: 28  # days