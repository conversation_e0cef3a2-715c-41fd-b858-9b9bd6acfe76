package main
import (
    "fmt"
    "log"
    "net/http"

    "github.com/gin-gonic/gin"
    "github.com/jinzhu/configor"

    "github.com/junhwong/rtk/internal/config"
    "github.com/junhwong/rtk/internal/server"
)

func main() {
       // Load configuration
    if err := config.LoadConfig("config.yaml"); err != nil {
        log.Fatalf("Failed to load configuration: %v", err)
    }    // Set Gin to release mode in production
    gin.SetMode(gin.ReleaseMode)

    // Create a default gin router
    router := gin.Default()

    // Add a health check endpoint
    router.GET("/health", func(c *gin.Context) {
    }